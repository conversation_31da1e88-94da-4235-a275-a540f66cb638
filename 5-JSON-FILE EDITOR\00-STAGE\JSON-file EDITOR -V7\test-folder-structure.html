<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بنية المجلدات</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        input[type="file"] { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>اختبار بنية المجلدات المتداخلة</h1>
    
    <div class="test-section">
        <h3>اختبار تحميل المجلد</h3>
        <input type="file" id="folderInput" webkitdirectory directory multiple>
        <button onclick="testFolderStructure()">اختبار البنية</button>
        <div id="result" class="result"></div>
    </div>

    <script>
        function testFolderStructure() {
            const folderFiles = document.getElementById('folderInput').files;
            const result = document.getElementById('result');
            
            if (!folderFiles || folderFiles.length === 0) {
                result.innerHTML = 'الرجاء اختيار مجلد';
                return;
            }
            
            // تنظيم الملفات حسب البنية
            let folderStructure = {};
            Array.from(folderFiles).forEach(file => {
                if (!file.name.toLowerCase().endsWith('.json')) return;
                
                const path = file.webkitRelativePath;
                const parts = path.split('/');
                parts.shift(); // إزالة اسم المجلد الرئيسي
                
                if (parts.length === 1) {
                    // ملف في المجلد الرئيسي
                    if (!folderStructure['root']) {
                        folderStructure['root'] = [];
                    }
                    folderStructure['root'].push(file);
                } else {
                    // ملف في مجلد فرعي
                    const folder = parts.slice(0, -1).join('/');
                    if (!folderStructure[folder]) {
                        folderStructure[folder] = [];
                    }
                    folderStructure[folder].push(file);
                }
            });
            
            // عرض النتائج
            let html = '<h4>بنية المجلدات:</h4>';
            Object.keys(folderStructure).sort().forEach(folderPath => {
                const files = folderStructure[folderPath];
                const folderName = folderPath === 'root' ? 'الملفات الرئيسية' : folderPath;
                html += `<div><strong>${folderName}</strong> (${files.length} ملف):</div>`;
                files.forEach(file => {
                    html += `<div style="margin-right: 20px;">- ${file.name}</div>`;
                });
                html += '<br>';
            });
            
            result.innerHTML = html;
        }
    </script>
</body>
</html>
