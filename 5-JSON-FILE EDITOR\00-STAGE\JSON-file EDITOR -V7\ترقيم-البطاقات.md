# ميزة ترقيم البطاقات

## الوصف
تم إضافة ميزة ترقيم تلقائي لجميع بطاقات الأفلام المعروضة. كل بطاقة تحمل الآن رقم ترتيبها في الملف الأصلي.

## المميزات

### 1. الترقيم التلقائي
- كل بطاقة فيلم تحمل رقم ترتيبها (1، 2، 3، إلخ)
- الترقيم يبدأ من 1 وليس من 0
- يظهر الرقم في الزاوية اليمنى العلوية للبطاقة

### 2. التصميم الجذاب
- **شكل دائري**: الرقم يظهر في دائرة ملونة
- **ألوان متدرجة**: خلفية بتدرج أحمر جميل
- **حدود بيضاء**: إطار أبيض يجعل الرقم أكثر وضوحاً
- **ظلال**: تأثير ظل لإعطاء عمق للرقم

### 3. التأثيرات التفاعلية
- **تكبير عند التمرير**: الرقم يكبر قليلاً عند تمرير الماوس فوق البطاقة
- **تأثير الظل**: زيادة شدة الظل عند التفاعل
- **انتقال سلس**: جميع التأثيرات تحدث بانتقال سلس

### 4. دعم الأرقام الكبيرة
- **الأرقام 1-99**: حجم عادي (40x40 بكسل)
- **الأرقام 100-999**: حجم أكبر قليلاً (45x45 بكسل) مع خط أصغر
- **الأرقام 1000+**: حجم أكبر (50x50 بكسل) مع خط أصغر

### 5. التحديث التلقائي
- عند حذف فيلم، يتم إعادة ترقيم جميع البطاقات تلقائياً
- عند تعديل فيلم، يبقى الترقيم كما هو
- عند البحث، تظهر الأرقام الأصلية للأفلام المطابقة

## كيفية الاختبار

### اختبار أساسي:
1. افتح التطبيق وانتقل لتبويب "عرض بطاقات الأفلام"
2. حمّل ملف `test-large-numbers.json` (يحتوي على 15 فيلم)
3. لاحظ الأرقام من 1 إلى 15 على البطاقات

### اختبار الحذف:
1. احذف الفيلم رقم 5
2. لاحظ أن الأرقام تم إعادة ترتيبها (الفيلم السادق أصبح رقم 5)

### اختبار البحث:
1. ابحث عن "فيلم رقم 1"
2. لاحظ أن البطاقة المطابقة تحتفظ برقمها الأصلي

### اختبار المجلدات المتداخلة:
1. حمّل مجلد `test-nested-folders`
2. افتح أي ملف JSON
3. لاحظ ترقيم البطاقات في كل ملف منفصل

## التفاصيل التقنية

### موقع الرقم:
- **الموضع**: الزاوية اليمنى العلوية
- **المسافة من الحافة**: 10 بكسل من الأعلى والجانب
- **الطبقة**: فوق الصورة (z-index: 10)

### الألوان:
- **الخلفية**: تدرج من #ff6b6b إلى #ee5a52
- **النص**: أبيض
- **الحدود**: أبيض بسماكة 3 بكسل
- **الظل**: أحمر شفاف

### الخطوط:
- **الحجم العادي**: 16px
- **الأرقام الكبيرة**: 14px
- **الأرقام الكبيرة جداً**: 12px
- **الوزن**: bold

## الملفات المتأثرة

### JavaScript:
- تحديث دالة `createMovieCard()` لإضافة الرقم
- إضافة منطق للأرقام الكبيرة
- الحفاظ على الترقيم في جميع السيناريوهات

### CSS:
- إضافة أنماط `.movie-number`
- أنماط للأرقام الكبيرة
- تأثيرات التفاعل والانتقال

## نصائح للاستخدام

1. **للملفات الكبيرة**: الترقيم يساعد في تتبع موقع كل فيلم
2. **للتنظيم**: يمكن استخدام الأرقام كمرجع عند التعديل
3. **للمقارنة**: سهولة مقارنة ترتيب الأفلام بين الملفات المختلفة
4. **للإحصائيات**: معرفة العدد الإجمالي للأفلام بسرعة
